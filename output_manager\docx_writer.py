import os
from docx import Document
from docx.shared import Inches
from typing import List, Dict, Any

class DocxWriter:
    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

    def write_quiz_to_docx(self, quiz_data: Dict[str, Any], filename: str = "generated_quiz.docx"):
        document = Document()
        
        # Add a title
        document.add_heading('生成的试题', level=1)

        questions = quiz_data.get('questions', [])
        
        for i, question_obj in enumerate(questions):
            question_type = question_obj.get('type', '未知').strip()
            question_text = question_obj.get('question', '').strip()
            options_text = question_obj.get('options', '').strip()
            answer_text = question_obj.get('answer', '').strip()
            difficulty_text = question_obj.get('difficulty', '简单').strip()
            explanation_text = question_obj.get('explanation', '暂无解析').strip()
            knowledge_points_text = question_obj.get('knowledge_points', '暂无知识点').strip()

            document.add_paragraph(f"{i+1}.【{question_type}】{question_text}")
            
            if question_type in ["单选", "多选", "排序"]:
                options = options_text.split('|')
                for j, option in enumerate(options):
                    # Convert index to A, B, C...
                    option_label = chr(65 + j)
                    document.add_paragraph(f"{option_label}、{option}")
            elif question_type == "填空":
                # For fill-in-the-blank, the question text already contains placeholders
                pass
            
            document.add_paragraph(f"正确答案： {answer_text}")
            document.add_paragraph(f"题目难度：{difficulty_text}")
            document.add_paragraph(f"答案解析：{explanation_text}")
            document.add_paragraph(f"知识点：{knowledge_points_text}")
            document.add_paragraph("\n") # Add an empty line for spacing

        filepath = os.path.join(self.output_dir, filename)
        document.save(filepath)
        print(f"试题已成功保存到：{filepath}") 